'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { MSME, Analytics } from '@/types';
import { api } from '@/lib/api';
import {
  FileText,
  Download,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  Users,
  MessageSquare,
  Target,
  RefreshCw,
  Filter,
  Building2,
  MapPin,
  Clock
} from 'lucide-react';

interface ScoreTrend {
  period: string;
  average_score: number;
  high_risk_count: number;
  medium_risk_count: number;
  low_risk_count: number;
}

interface SignalPattern {
  source: string;
  total_signals: number;
  avg_per_msme: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  last_7_days: number;
}

interface NudgeMetrics {
  total_sent: number;
  delivery_rate: number;
  response_rate: number;
  effectiveness_score: number;
  by_medium: {
    whatsapp: { sent: number; delivered: number; responded: number };
    email: { sent: number; delivered: number; responded: number };
    sms: { sent: number; delivered: number; responded: number };
  };
}

interface BusinessTypePerformance {
  business_type: string;
  avg_score: number;
  msme_count: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  trend: 'improving' | 'declining' | 'stable';
}

export function ReportsAnalytics() {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [scoreTrends, setScoreTrends] = useState<ScoreTrend[]>([]);
  const [signalPatterns, setSignalPatterns] = useState<SignalPattern[]>([]);
  const [nudgeMetrics, setNudgeMetrics] = useState<NudgeMetrics | null>(null);
  const [businessPerformance, setBusinessPerformance] = useState<BusinessTypePerformance[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [selectedBusinessType, setSelectedBusinessType] = useState('all');

  useEffect(() => {
    fetchData();
  }, [selectedPeriod]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [portfolioData, analyticsData] = await Promise.all([
        api.getPortfolio(),
        api.getAnalytics()
      ]);
      
      setMsmes(portfolioData);
      setAnalytics(analyticsData);
      
      // Generate mock analytics data
      generateScoreTrends();
      generateSignalPatterns();
      generateNudgeMetrics();
      generateBusinessPerformance(portfolioData);
      
    } catch (error) {
      console.error('Failed to fetch reports data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateScoreTrends = () => {
    const trends: ScoreTrend[] = [];
    const periods = selectedPeriod === '7d' ? 7 : selectedPeriod === '30d' ? 30 : 90;
    
    for (let i = periods - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      trends.push({
        period: date.toISOString().split('T')[0],
        average_score: 650 + Math.random() * 100 - 50,
        high_risk_count: Math.floor(Math.random() * 5) + 2,
        medium_risk_count: Math.floor(Math.random() * 8) + 5,
        low_risk_count: Math.floor(Math.random() * 10) + 8,
      });
    }
    
    setScoreTrends(trends);
  };

  const generateSignalPatterns = () => {
    const patterns: SignalPattern[] = [
      {
        source: 'gst',
        total_signals: 156,
        avg_per_msme: 7.8,
        trend: 'increasing',
        last_7_days: 23
      },
      {
        source: 'upi',
        total_signals: 134,
        avg_per_msme: 6.7,
        trend: 'stable',
        last_7_days: 19
      },
      {
        source: 'reviews',
        total_signals: 89,
        avg_per_msme: 4.5,
        trend: 'increasing',
        last_7_days: 15
      },
      {
        source: 'instagram',
        total_signals: 67,
        avg_per_msme: 3.4,
        trend: 'decreasing',
        last_7_days: 8
      },
      {
        source: 'justdial',
        total_signals: 45,
        avg_per_msme: 2.3,
        trend: 'stable',
        last_7_days: 6
      }
    ];
    
    setSignalPatterns(patterns);
  };

  const generateNudgeMetrics = () => {
    const metrics: NudgeMetrics = {
      total_sent: 234,
      delivery_rate: 94.5,
      response_rate: 23.8,
      effectiveness_score: 78.2,
      by_medium: {
        whatsapp: { sent: 145, delivered: 142, responded: 38 },
        email: { sent: 67, delivered: 61, responded: 12 },
        sms: { sent: 22, delivered: 19, responded: 6 }
      }
    };
    
    setNudgeMetrics(metrics);
  };

  const generateBusinessPerformance = (msmeData: MSME[]) => {
    const businessTypes = ['retail', 'b2b', 'manufacturing', 'services'];
    const performance: BusinessTypePerformance[] = [];
    
    businessTypes.forEach(type => {
      const typeMsmes = msmeData.filter(msme => msme.business_type === type);
      if (typeMsmes.length > 0) {
        const avgScore = typeMsmes.reduce((sum, msme) => sum + msme.current_score, 0) / typeMsmes.length;
        const riskDist = {
          green: typeMsmes.filter(m => m.risk_band === 'green').length,
          yellow: typeMsmes.filter(m => m.risk_band === 'yellow').length,
          red: typeMsmes.filter(m => m.risk_band === 'red').length,
        };
        
        performance.push({
          business_type: type,
          avg_score: Math.round(avgScore),
          msme_count: typeMsmes.length,
          risk_distribution: riskDist,
          trend: Math.random() > 0.5 ? 'improving' : Math.random() > 0.3 ? 'stable' : 'declining'
        });
      }
    });
    
    setBusinessPerformance(performance);
  };

  const downloadReport = (format: 'pdf' | 'csv') => {
    // Mock download functionality
    const filename = `credit-chakra-report-${new Date().toISOString().split('T')[0]}.${format}`;
    console.log(`Downloading ${filename}...`);
    
    // In a real implementation, this would generate and download the actual file
    const blob = new Blob([`Mock ${format.toUpperCase()} report data`], { 
      type: format === 'pdf' ? 'application/pdf' : 'text/csv' 
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'decreasing':
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getBusinessTypeLabel = (type: string) => {
    switch (type) {
      case 'retail': return 'Retail';
      case 'b2b': return 'B2B';
      case 'manufacturing': return 'Manufacturing';
      case 'services': return 'Services';
      default: return type;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="grid gap-6 md:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
            Reports & Analytics
          </h1>
          <p className="text-muted-foreground">
            Comprehensive insights and downloadable reports for your MSME portfolio
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => downloadReport('pdf')}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            PDF Report
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => downloadReport('csv')}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            CSV Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <BarChart3 className="h-5 w-5" />
              </div>
              Avg Portfolio Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-700 mb-2">
              {analytics ? Math.round(msmes.reduce((sum, msme) => sum + msme.current_score, 0) / msmes.length) : 0}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-green-600">+2.3% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <Activity className="h-5 w-5" />
              </div>
              Total Signals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-700 mb-2">
              {signalPatterns.reduce((sum, pattern) => sum + pattern.total_signals, 0)}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-green-600">+12% this period</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <div className="p-2 bg-orange-500/10 rounded-lg">
                <MessageSquare className="h-5 w-5" />
              </div>
              Nudges Sent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-700 mb-2">
              {nudgeMetrics?.total_sent || 0}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Target className="h-3 w-3 text-blue-500" />
              <span className="text-blue-600">{nudgeMetrics?.delivery_rate || 0}% delivery rate</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <Users className="h-5 w-5" />
              </div>
              Active MSMEs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-700 mb-2">
              {analytics?.total_msmes || 0}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Clock className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600">Last updated today</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="trends" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends">Score Trends</TabsTrigger>
          <TabsTrigger value="signals">Signal Analysis</TabsTrigger>
          <TabsTrigger value="nudges">Nudge Effectiveness</TabsTrigger>
          <TabsTrigger value="business">Business Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Score Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Portfolio Score Trends
                </CardTitle>
                <CardDescription>
                  Average credit scores over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span>Current Average</span>
                    <span className="font-semibold">
                      {scoreTrends.length > 0 ? Math.round(scoreTrends[scoreTrends.length - 1].average_score) : 0}
                    </span>
                  </div>
                  <div className="h-48 flex items-end justify-between gap-1">
                    {scoreTrends.slice(-10).map((trend, index) => (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-blue-500 rounded-t"
                          style={{
                            height: `${(trend.average_score / 1000) * 100}%`,
                            minHeight: '4px'
                          }}
                        />
                        <span className="text-xs text-muted-foreground mt-1 rotate-45 origin-left">
                          {new Date(trend.period).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Risk Distribution Over Time */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Risk Distribution Trends
                </CardTitle>
                <CardDescription>
                  Changes in risk levels over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {scoreTrends.slice(-5).map((trend, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>{new Date(trend.period).toLocaleDateString()}</span>
                        <span className="text-muted-foreground">
                          Total: {trend.high_risk_count + trend.medium_risk_count + trend.low_risk_count}
                        </span>
                      </div>
                      <div className="flex h-2 rounded-full overflow-hidden">
                        <div
                          className="bg-green-500"
                          style={{
                            width: `${(trend.low_risk_count / (trend.high_risk_count + trend.medium_risk_count + trend.low_risk_count)) * 100}%`
                          }}
                        />
                        <div
                          className="bg-yellow-500"
                          style={{
                            width: `${(trend.medium_risk_count / (trend.high_risk_count + trend.medium_risk_count + trend.low_risk_count)) * 100}%`
                          }}
                        />
                        <div
                          className="bg-red-500"
                          style={{
                            width: `${(trend.high_risk_count / (trend.high_risk_count + trend.medium_risk_count + trend.low_risk_count)) * 100}%`
                          }}
                        />
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Low: {trend.low_risk_count}</span>
                        <span>Medium: {trend.medium_risk_count}</span>
                        <span>High: {trend.high_risk_count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="signals" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Signal Sources */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Signal Sources Analysis
                </CardTitle>
                <CardDescription>
                  Data ingestion patterns by source
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {signalPatterns.map((pattern, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 rounded-full bg-blue-500" />
                        <div>
                          <p className="font-medium text-sm capitalize">{pattern.source}</p>
                          <p className="text-xs text-muted-foreground">
                            {pattern.avg_per_msme} avg per MSME
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">{pattern.total_signals}</span>
                          {getTrendIcon(pattern.trend)}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {pattern.last_7_days} in last 7 days
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Signal Frequency */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Signal Frequency
                </CardTitle>
                <CardDescription>
                  Signal collection patterns
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {signalPatterns.reduce((sum, p) => sum + p.total_signals, 0)}
                      </div>
                      <div className="text-sm text-blue-600">Total Signals</div>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {Math.round(signalPatterns.reduce((sum, p) => sum + p.avg_per_msme, 0) / signalPatterns.length)}
                      </div>
                      <div className="text-sm text-green-600">Avg per MSME</div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Recent Activity (7 days)</h4>
                    {signalPatterns.map((pattern, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{pattern.source}</span>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={(pattern.last_7_days / Math.max(...signalPatterns.map(p => p.last_7_days))) * 100}
                            className="w-20 h-2"
                          />
                          <span className="text-sm font-medium w-8">{pattern.last_7_days}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="nudges" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Nudge Effectiveness */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Nudge Effectiveness
                </CardTitle>
                <CardDescription>
                  Overall nudge campaign performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">{nudgeMetrics?.total_sent || 0}</div>
                      <div className="text-sm text-muted-foreground">Total Sent</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">{nudgeMetrics?.delivery_rate || 0}%</div>
                      <div className="text-sm text-muted-foreground">Delivery Rate</div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Response Rate</span>
                      <span className="font-semibold">{nudgeMetrics?.response_rate || 0}%</span>
                    </div>
                    <Progress value={nudgeMetrics?.response_rate || 0} className="h-2" />

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Effectiveness Score</span>
                      <span className="font-semibold">{nudgeMetrics?.effectiveness_score || 0}%</span>
                    </div>
                    <Progress value={nudgeMetrics?.effectiveness_score || 0} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Nudge by Medium */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Performance by Medium
                </CardTitle>
                <CardDescription>
                  Effectiveness across different channels
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {nudgeMetrics && Object.entries(nudgeMetrics.by_medium).map(([medium, stats]) => (
                    <div key={medium} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium capitalize">{medium}</span>
                        <Badge variant="outline">
                          {Math.round((stats.delivered / stats.sent) * 100)}% delivered
                        </Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div className="text-center">
                          <div className="font-semibold">{stats.sent}</div>
                          <div className="text-muted-foreground">Sent</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold">{stats.delivered}</div>
                          <div className="text-muted-foreground">Delivered</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold">{stats.responded}</div>
                          <div className="text-muted-foreground">Responded</div>
                        </div>
                      </div>
                      <Progress
                        value={(stats.responded / stats.sent) * 100}
                        className="mt-2 h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="business" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Business Type Performance
              </CardTitle>
              <CardDescription>
                Comparative analysis across different business types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {businessPerformance.map((business, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold">{getBusinessTypeLabel(business.business_type)}</h4>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">Avg Score: {business.avg_score}</span>
                        {getTrendIcon(business.trend)}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span>MSMEs: {business.msme_count}</span>
                        <span>Risk Distribution</span>
                      </div>

                      <div className="flex h-2 rounded-full overflow-hidden">
                        <div
                          className="bg-green-500"
                          style={{
                            width: `${(business.risk_distribution.green / business.msme_count) * 100}%`
                          }}
                        />
                        <div
                          className="bg-yellow-500"
                          style={{
                            width: `${(business.risk_distribution.yellow / business.msme_count) * 100}%`
                          }}
                        />
                        <div
                          className="bg-red-500"
                          style={{
                            width: `${(business.risk_distribution.red / business.msme_count) * 100}%`
                          }}
                        />
                      </div>

                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Low: {business.risk_distribution.green}</span>
                        <span>Medium: {business.risk_distribution.yellow}</span>
                        <span>High: {business.risk_distribution.red}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
